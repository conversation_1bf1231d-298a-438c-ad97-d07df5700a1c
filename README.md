# E*TRADE API App

This application connects to the E*TRADE APIs to fetch the current price of a stock. It is written in Python and uses the latest Python features.

## Features
- Connects to E*TRADE APIs.
- Fetches the current price of the stock symbol `KSS`.

## Setup
1. Install the required Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

## Requirements
- Python 3.8 or later

## License
This project is licensed under the MIT License.
