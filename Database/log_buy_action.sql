CREATE DEFINER=`root`@`localhost` PROCEDURE `log_buy_action`(	in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_activity_id int,
                                in i_order_id int
                                )
BEGIN

declare l_activity_id int;
declare l_inventory_id int;
declare l_avg_inventory_id int;
declare l_avg_inventory_cnt int;  
declare l_avg_inventory_price decimal(10,5);
declare l_avg_price decimal(10,5);


	set l_activity_id = i_activity_id;
    
    -- generate next id for inventory 
	insert into inventory_sequence values();
	set l_inventory_id = LAST_INSERT_ID();
   
	-- get record if exists to be updated with avg sale price before new buy record is inseted
	select IFNULL(id,0), IFNULL(cnt,0), IFNULL(price,0)
	into l_avg_inventory_id , l_avg_inventory_cnt, l_avg_inventory_price
	from inventory 
	where symbol=i_symbol
	and id = (select max(id) from inventory where symbol=i_symbol);
        
	insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
    values (l_activity_id, 'buy', i_price, i_cnt, i_date, i_symbol,i_order_id);

    -- insert the new buy record data
	insert into inventory (id, cnt,price, process_date, activity_id,symbol) 
    values (l_inventory_id,i_cnt, i_price, i_date, l_activity_id, i_symbol);

	-- if previous record existed
    if l_avg_inventory_id>0 then
		--  calculate the avg price on last 2 record (newly inserted and previous last)
		set l_avg_price = round( ( (i_cnt*i_price) + 
							(l_avg_inventory_cnt*l_avg_inventory_price) 
						  ) / (i_cnt+l_avg_inventory_cnt) ,4);
        
        -- update the previous record after the new record is inserted with the avg sale price for both
        update inventory
        set sale_price_with_next = l_avg_price
        where id = l_avg_inventory_id;
        
    end if;

END