#!/bin/zsh
# Launch tmux with two vertical panes: left runs python app.py, right tails the action log

SESSION="sm_dashboard"
LOGFILE="action_log.log"

# Kill existing session if it exists
if tmux has-session -t $SESSION 2>/dev/null; then
    tmux kill-session -t $SESSION
fi

# Start new tmux session detached
tmux new-session -d -s $SESSION

# Split window vertically (right pane)
tmux split-window -h -t $SESSION

# Detect if running on this laptop (MacBook) by hostname
MY_HOSTNAME="$(hostname)"

# Aactivating venv-mbp if hostname contains 'macbook', otherwise activate venv
# Run python app.py in the left pane
if [[ "${MY_HOSTNAME:l}" == *macbook* && -d "venv-mbp" ]]; then
    tmux send-keys -t $SESSION:0.0 'source venv-mbp/bin/activate && python3 app.py' C-m
elif [ -d "venv" ]; then
    tmux send-keys -t $SESSION:0.0 'source venv/bin/activate && python3 app.py' C-m
    #tmux send-keys -t $SESSION:0.0 'source venv/bin/activate && python app.py; tmux kill-session -t sm_dashboard' C-m
else
    tmux send-keys -t $SESSION:0.0 'python3 app.py' C-m
    #tmux send-keys -t $SESSION:0.0 'python3 app.py;  tmux kill-session -t sm_dashboard' C-m
fi

# Run tail -f in the right pane
tmux send-keys -t $SESSION:0.1 "tail -f $LOGFILE" C-m

# Select the left pane by default
tmux select-pane -t $SESSION:0.0

# Attach to the session
tmux attach-session -t $SESSION
