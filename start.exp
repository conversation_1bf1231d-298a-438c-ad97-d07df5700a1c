#!/usr/bin/expect -f

# Optional: Prevent timeouts
set timeout -1

# Start the Python app with a pseudo-terminal
spawn -noecho python app.py

# Optional: log output
log_file etrade_output.log

# --- Interact with the app ---
expect "Choose E*TRADE environment"
# send_user "Enter environment (0=NONE, 1=SANDBOX, 2=PROD): "
expect_user -re "(.*)\n"
send "$expect_out(1,string)\r"

expect "Please accept agreement and enter verification code from browser:"
# send_user "Paste verification code: "
expect_user -re "(.*)\n"
send "$expect_out(1,string)\r"

# --- Detach the process ---
# First, get the PID of the spawned process
set pid [exp_pid]
puts "Detaching process PID=$pid"

# Let the app run freely
exec sh -c "kill -CONT $pid; disown $pid" &
exit
