Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: 
E*TRADE prompt detected.
Enter environment (0=NONE, 1=SANDBOX, 2=PROD): can't read "expect_out(1,string)": no such element in array
    while executing
"send "$expect_out(1,string)\r""
    (file "./start.exp" line 11)
Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: 
E*TRADE prompt detected.
Enter environment (0=NONE, 1=SANDBOX, 2=PROD): 1
1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=sx27u/45czd6Fue0Lii5pqLAnI9fwqe6NESZXs2RqJM=

 - 0) NONE 1) SANDBOX 2) PROD: 1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=sx27u/45czd6Fue0Lii5pqLAnI9fwqe6NESZXs2RqJM=
Please accept agreement and enter verification code from browser: Enter verification code: 1
Traceback (most recent call last):
  File "/Users/<USER>/new/sm/.venv/lib/python3.11/site-packages/rauth/service.py", line 21, in process_token_request
    return tuple(data[key] for key in args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/new/sm/.venv/lib/python3.11/site-packages/rauth/service.py", line 21, in <genexpr>
    return tuple(data[key] for key in args)
                 ~~~~^^^^^
KeyError: 'oauth_token'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/new/sm/app.py", line 736, in <module>
    oauth_session = get_oauth_session()
                    ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/new/sm/app.py", line 185, in get_oauth_session
    return etrade.get_auth_session(request_token,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/new/sm/.venv/lib/python3.11/site-packages/rauth/service.py", line 356, in get_auth_session
    token = self.get_access_token(request_token,
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/new/sm/.venv/lib/python3.11/site-packages/rauth/service.py", line 332, in get_access_token
    process_token_request(r, decoder, key_token, key_token_secret)
  File "/Users/<USER>/new/sm/.venv/lib/python3.11/site-packages/rauth/service.py", line 24, in process_token_request
    raise KeyError(PROCESS_TOKEN_ERROR.format(key=bad_key, raw=r.content))
KeyError: 'Decoder failed to handle oauth_token with data as returned by provider. A different decoder may be needed. Provider returned: b\'<!doctype html><html lang="en"><head><title>HTTP Status 401 \\xe2\\x80\\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \\xe2\\x80\\x93 Unauthorized</h1><hr class="line" /><p><b>Type</b> Status Report</p><p><b>Message</b> oauth_problem=token_rejected</p><p><b>Description</b> The request has not been applied to the target resource because it lacks valid authentication credentials for that resource.</p><hr class="line" /><h3></h3></body></html>\''
Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: 
E*TRADE prompt detected.
Enter environment (0=NONE, 1=SANDBOX, 2=PROD): 1
1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=QWqWmm5QZ6lOYoOamnPXGmO7p7tW8TdfBCTHWQr4R3A=

 - 0) NONE 1) SANDBOX 2) PROD: 1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=QWqWmm5QZ6lOYoOamnPXGmO7p7tW8TdfBCTHWQr4R3A=
Please accept agreement and enter verification code from browser: Enter verification code: 7J86W
7J86W
[3J[H[2JE*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

========= PRICE STATUS =========
Last Checked: 2025-06-26 23:14:29
WEN : $17.1248                                 
KSS : $30.3732                                 
UA  : $11.5303                                 
AAPL: $19.8929                                 
TSLA: $20.4641                                 
PLCE: $30.5851                                 
GDRX: $22.2246                                 
DNUT: $ 4.9752                                 
JACK: $25.1779                                 
ADT : $ 1.9919                                 
================================

================================ INVENTORY ================================
Inventory for WEN: qty=5, min=$11.30, max=$11.32, avg=$11.31
Inventory for KSS: qty=5, min=$3.95, max=$3.95, avg=$3.95
No remaining inventory for UA
Inventory for AAPL: qty=12, min=$1.49, max=$199.95, avg=$149.36
Inventory for TSLA: qty=14, min=$2.87, max=$353.94, avg=$274.08
No remaining inventory for PLCE
No remaining inventory for GDRX
No remaining inventory for DNUT
No remaining inventory for JACK
No remaining inventory for ADT
===========================================================================

================================ PROFITS ================================
Profit for WEN: qty=370, total sales=$277.79, total profit=$232.87, today's profit=$0.00
Profit for KSS: qty=398, total sales=$580.30, total profit=$307.16, today's profit=$0.00
Profit for UA: qty=443, total sales=$172.20, total profit=$32.48, today's profit=$0.00
Profit for AAPL: qty=11, total sales=$408.91, total profit=$62.42, today's profit=$0.00
Profit for TSLA: qty=100, total sales=$7471.46, total profit=$221.64, today's profit=$0.00
Profit for PLCE: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for GDRX: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for DNUT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for JACK: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for ADT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
=========================================================================


See action_log.log for rolling log of actions and profits.

Windows: stream_action_log.ps1
Linux/Mac: tail -f action_log.log
[3J[H[2JE*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

========= PRICE STATUS =========
Last Checked: 2025-06-26 23:14:29
WEN : $17.1248                                 
KSS : $30.3732                                 
UA  : $11.5303                                 
AAPL: $19.8929                                 
TSLA: $20.4641                                 
PLCE: $30.5851                                 
GDRX: $22.2246                                 
DNUT: $ 4.9752                                 
JACK: $25.1779                                 
ADT : $ 1.9919                                 
================================

================================ INVENTORY ================================
No remaining inventory for WEN
No remaining inventory for KSS
No remaining inventory for UA
Inventory for AAPL: qty=9, min=$197.80, max=$199.95, avg=$198.65
Inventory for TSLA: qty=11, min=$343.14, max=$353.94, avg=$348.05
No remaining inventory for PLCE
No remaining inventory for GDRX
No remaining inventory for DNUT
No remaining inventory for JACK
No remaining inventory for ADT
===========================================================================

================================ PROFITS ================================
Profit for WEN: qty=375, total sales=$312.03, total profit=$261.93, today's profit=$29.06
Profit for KSS: qty=403, total sales=$610.68, total profit=$439.26, today's profit=$132.10
Profit for UA: qty=443, total sales=$172.20, total profit=$32.48, today's profit=$0.00
Profit for AAPL: qty=14, total sales=$428.80, total profit=$117.62, today's profit=$55.20
Profit for TSLA: qty=103, total sales=$7491.92, total profit=$274.41, today's profit=$52.77
Profit for PLCE: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for GDRX: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for DNUT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for JACK: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for ADT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
=========================================================================


See action_log.log for rolling log of actions and profits.

Windows: stream_action_log.ps1
Linux/Mac: tail -f action_log.log
[3J[H[2JE*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

========= PRICE STATUS =========
Last Checked: 2025-06-26 23:14:48
WEN : $22.9157                                 
KSS : $ 9.2345                                 
UA  : $10.0964                                 
AAPL: $17.0604                                 
TSLA: $ 5.9493                                 
PLCE: $15.0515                                 
GDRX: $11.5405                                 
DNUT: $ 1.6472                                 
JACK: $14.4676                                 
ADT : $11.7230                                 
================================

================================ INVENTORY ================================
No remaining inventory for WEN
No remaining inventory for KSS
No remaining inventory for UA
Inventory for AAPL: qty=9, min=$197.80, max=$199.95, avg=$198.65
Inventory for TSLA: qty=11, min=$343.14, max=$353.94, avg=$348.05
No remaining inventory for PLCE
No remaining inventory for GDRX
No remaining inventory for DNUT
No remaining inventory for JACK
No remaining inventory for ADT
===========================================================================

================================ PROFITS ================================
Profit for WEN: qty=375, total sales=$312.03, total profit=$261.93, today's profit=$29.06
Profit for KSS: qty=403, total sales=$610.68, total profit=$439.26, today's profit=$132.10
Profit for UA: qty=443, total sales=$172.20, total profit=$32.48, today's profit=$0.00
Profit for AAPL: qty=14, total sales=$428.80, total profit=$117.62, today's profit=$55.20
Profit for TSLA: qty=103, total sales=$7491.92, total profit=$274.41, today's profit=$52.77
Profit for PLCE: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for GDRX: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for DNUT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for JACK: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
Profit for ADT: qty=0, total sales=$0.00, total profit=$0.00, today's profit=$0.00
=========================================================================


See action_log.log for rolling log of actions and profits.

Windows: stream_action_log.ps1
Linux/Mac: tail -f action_log.log

^Z^Z^CStopped by user.
Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: Enter environment (0=NONE, 1=SANDBOX, 2=PROD): 1
1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=SbzfabDAjolStuYaW38tXv4Dn5shOl5OBTUWjYbjukY=
Please accept agreement and enter verification code from browser: Paste verification code: Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: 1
1
E*TRADE API App
Press Ctrl+C to stop

Using SANDBOX environment

https://us.etrade.com/e/t/etws/authorize?key=fa4d6b669bbbd6b316d6e926ba0f4399&token=2yvM03N8LJpIYTCwb+oKhDH4N/UPqXxBo7t6PkMkS9c=
Please accept agreement and enter verification code from browser: 2ICRY
