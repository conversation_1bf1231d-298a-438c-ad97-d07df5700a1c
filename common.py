def log_to_action_log(msg = ""):
    from datetime import datetime
    timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    if msg.strip() == "":
        log_line = ""
    elif msg.strip() == "time":
        log_line = f"============{timestamp}============"
    else:
        log_line = f"{msg}"
    with open("action_log.log", "a") as f:
        if msg.strip() == "time":
            f.write("\n" + log_line + "\n")
        else:
            f.write(log_line + "\n")
