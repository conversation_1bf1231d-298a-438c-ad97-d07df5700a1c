import functools
import configparser
import pymysql
import sshtunnel
from decimal import Decimal
from common import log_to_action_log
from sshtunnel import BaseSSHTunnelForwarderError
import time
import pymysql

# Decorator for global MySQL auto-reconnect
def with_reconnect(db_func):
    @functools.wraps(db_func)
    def wrapper(*args, **kwargs):
        max_retries = 2
        for attempt in range(max_retries):
            try:
                return db_func(*args, **kwargs)
            except (pymysql.err.OperationalError, pymysql.err.InterfaceError) as e:
                # 2013: Lost connection to MySQL server during query
                # 2006: MySQL server has gone away
                # 0: InterfaceError (connection dropped)
                if hasattr(e, 'args') and len(e.args) > 0 and e.args[0] in (2013, 2006, 0):
                    from db_utils import close_connection, get_connection
                    print(f"Reconnecting to database after error: {e}")
                    close_connection()
                    # Replace conn in args if present
                    args = list(args)
                    if args and hasattr(args[0], 'cursor'):
                        args[0] = get_connection()
                    elif 'conn' in kwargs:
                        kwargs['conn'] = get_connection()
                    continue
                else:
                    raise
            except Exception as e:
                raise
        raise RuntimeError("Failed to reconnect to the database or SSH tunnel after multiple attempts.")
    return wrapper

def get_db_config():
    config = configparser.ConfigParser()
    config.read('config.ini')
    db_cfg = config['DB']
    # Try to use use_sandbox from app.py if available via builtins
    import builtins

    use_sandbox = getattr(builtins, 'use_sandbox', None)

    if use_sandbox:
        db_cfg['DB_NAME'] = db_cfg.get('SB_DB_NAME', db_cfg['DB_NAME'])
    
    return db_cfg

# Persistent connection and (optional) tunnel
_persistent_tunnel = None
_persistent_conn = None

def get_connection(retry_count=0):
    global _persistent_tunnel, _persistent_conn
    if _persistent_conn is not None:
        return _persistent_conn
    
    try:
        db_cfg = get_db_config()
        use_ssh = db_cfg.getboolean('USE_SSH', fallback=False)
        db_charset = db_cfg.get('DB_CHARSET', 'utf8mb4')
        if use_ssh:
            _persistent_tunnel = sshtunnel.SSHTunnelForwarder(
                (db_cfg['SSH_HOST'], int(db_cfg['SSH_PORT'])),
                ssh_username=db_cfg['SSH_USER'],
                ssh_password=db_cfg.get('SSH_PASSWORD'),
                ssh_pkey=db_cfg.get('SSH_PKEY'),
                remote_bind_address=(db_cfg['DB_HOST'], int(db_cfg['DB_PORT'])),
                local_bind_address=('127.0.0.1',)
            )
            _persistent_tunnel.start()
            _persistent_conn = pymysql.connect(
                host='127.0.0.1',
                port=_persistent_tunnel.local_bind_port,
                user=db_cfg['DB_USER'],
                password=db_cfg['DB_PASSWORD'],
                database=db_cfg['DB_NAME'],
                charset=db_charset,
                autocommit=True
            )
        else:
            _persistent_conn = pymysql.connect(
                host=db_cfg['DB_HOST'],
                port=int(db_cfg['DB_PORT']),
                user=db_cfg['DB_USER'],
                password=db_cfg['DB_PASSWORD'],
                database=db_cfg['DB_NAME'],
                charset=db_charset,
                autocommit=True
            )
        return _persistent_conn
    except Exception as e:
        # infinite retries
        
        # if retry_count < 5:
            if isinstance(e, BaseSSHTunnelForwarderError):
                print(f"SSH tunnel error: {e}. Retrying in 5 seconds...")
                time.sleep(5)
            else:
                print(f"Database connection error: {e}. Retrying in 5 seconds...")
                time.sleep(5)
            return get_connection(retry_count + 1)
        #else:
        #    raise

def close_connection():
    global _persistent_tunnel, _persistent_conn
    if _persistent_conn is not None:
        _persistent_conn.close()
        _persistent_conn = None
    if _persistent_tunnel is not None:
        _persistent_tunnel.stop()
        _persistent_tunnel = None

@with_reconnect
def insert_stock_data(conn, symbol, data):
    # Table name is always the symbol in lowercase
    table = symbol.lower()
    import configparser
    config = configparser.ConfigParser()
    config.read('config.ini')
    db_charset = config['DB'].get('DB_CHARSET', 'utf8mb4')
    with conn.cursor() as cur:
        from datetime import datetime
        cur.execute(f"""
            CREATE TABLE IF NOT EXISTS `{table}` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `quote_timestamp` varchar(64) NOT NULL,
              `open` decimal(10,4) DEFAULT NULL,
              `high` decimal(10,4) DEFAULT NULL,
              `low` decimal(10,4) DEFAULT NULL,
              `close` decimal(10,4) DEFAULT NULL,
              `volume` int(11) DEFAULT NULL,
              `symbol` varchar(45) DEFAULT NULL,
              `inserted_at` datetime NOT NULL,
              PRIMARY KEY (`id`),
              KEY `quote_timestamp_IDX` (`quote_timestamp`)
            ) ENGINE=InnoDB DEFAULT CHARSET={db_charset};
        """)
        cur.execute("INSERT IGNORE INTO `" + table + "` (quote_timestamp, open, high, low, close, volume, symbol, inserted_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)", (
            data.get('time_record'),
            data['open'],
            data['high'],
            data['low'],
            data['close'],
            data['volume'],
            symbol.upper(),  # Symbol column is always uppercase
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))



@with_reconnect
def act(conn, i_price, i_date, symbol):
    """
    Calls the MySQL stored procedure get_action and returns {'action': ..., 'count': ...}
    """
    i_price = Decimal(str(i_price))

    with conn.cursor() as cur:
        # Call the stored procedure with parameters
        cur.callproc('get_action', (
            i_price,
            symbol,
            '',  # Output parameter for action
            0    # Output parameter for count
        ))
        
        # Get the output parameters
        # MySQL stored procedure OUT parameters are returned as the last result set
        cur.execute("SELECT @_get_action_2, @_get_action_3")
        action, count = cur.fetchone()
        
        return {'action': action, 'count': count}

@with_reconnect
def get_profit_summary(conn, symbol):
    """
    Returns the total profit for a symbol, and today's profit.
    """
    with conn.cursor() as cur:
        cur.execute('''
            SELECT
                SUM(i.cnt) AS total_count,
                SUM(a.price) AS total_sales,
                SUM((a.price - i.price) * i.cnt) AS total_profit,
                SUM(CASE WHEN DATE(s.process_date) = CURDATE() THEN (a.price - i.price) * i.cnt ELSE 0 END) AS today_profit
            FROM archive_sold s
            JOIN activity a ON s.activity_id = a.id
            JOIN inventory_arch i ON s.inventory_id = i.id
            WHERE a.symbol = %s
        ''', (symbol,))
        row = cur.fetchone()
        total_sales = row[1] if row and row[1] is not None else 0.0
        total_profit = row[2] if row and row[2] is not None else 0.0
        today_profit = row[3] if row and row[3] is not None else 0.0
        total_count = row[0] if row and row[0] is not None else 0
        message = (
            f"Profit for {symbol}: qty={total_count}, total sales=${total_sales:.2f}, "
            f"total profit=${total_profit:.2f}, today's profit=${today_profit:.2f}"
        )
        short_message = f"Total profit: ${total_profit:.2f}, Today: ${today_profit:.2f}"
        return {'message': message, 'short_message': short_message, 'total_profit': total_profit, 'today_profit': today_profit}

@with_reconnect
def get_inventory_summary(conn, symbol):
    with conn.cursor() as cur:
        # Get min, max, and avg price (weighted by count) for current inventory
        # Modified query to be compatible with ONLY_FULL_GROUP_BY mode
        cur.execute("""
            SELECT 
                i_sum.min_price, 
                i_sum.max_price, 
                i_sum.total_price, 
                i_sum.total_count, 
                COALESCE(min_price_count.count_at_min, 0) as count_at_min_price
            FROM 
                (SELECT 
                    symbol, 
                    MIN(price) min_price, 
                    MAX(price) max_price, 
                    SUM(price * cnt) total_price, 
                    SUM(cnt) total_count 
                FROM inventory 
                WHERE symbol = %s) i_sum
            LEFT JOIN 
                (SELECT 
                    symbol, 
                    price, 
                    SUM(cnt) as count_at_min 
                FROM inventory 
                WHERE symbol = %s
                GROUP BY symbol, price) min_price_count
            ON (min_price_count.symbol = i_sum.symbol AND min_price_count.price = i_sum.min_price)
        """, (symbol, symbol))
        
        row = cur.fetchone()
        if not row or row[3] is None or row[3] == 0:
            message = f"No remaining inventory for {symbol}"
            short_message = f"Total shares: 0"
            return {'message': message, 'short_message': short_message, "total_count": 0, "min_price": 0.0, "max_price": 0.0, "avg_price": 0.0, "count_at_min_price": 0}
        min_price = row[0]
        max_price = row[1]
        total_price = row[2]
        total_count = row[3]
        count_at_min_price = row[4]
        avg_price = (total_price / total_count) if total_count and total_price is not None else None
        message = f"Inventory for {symbol}: qty={total_count}, min=${min_price:.2f}, max=${max_price:.2f}, avg=${avg_price:.2f}"
        short_message = f"Total shares: {total_count} @ avg ${avg_price:.2f}"
        return {'message': message, 'short_message': short_message, "total_count": int(total_count), "min_price": float(min_price), "max_price": float(max_price), "avg_price": float(avg_price), "count_at_min_price": int(count_at_min_price)}
          
@with_reconnect
def get_full_profit_summary(conn, symbols):
    profit_summary = {}
    for sym in symbols:
        profit = get_profit_summary(conn, sym)
        profit_summary[sym] = profit['message'] if isinstance(profit, dict) and 'message' in profit else profit
    return profit_summary

@with_reconnect
def get_full_inventory_summary(conn, symbols):
    inventory_summary = {}
    for sym in symbols:
        inv = get_inventory_summary(conn, sym)
        inventory_summary[sym] = inv
    return inventory_summary


def get_purchase_summary(conn, symbol, purchase_count, purchase_price, activity_id):
    return {
        'message': f"+++ {symbol}: bought {purchase_count} @ {purchase_price:.4f}",
        'short_message': f"Bought {purchase_count} {symbol} @ ${purchase_price:.2f}"
    }

@with_reconnect
def get_sale_summary(conn, symbol, sale_count, sale_price, activity_id):
    with conn.cursor() as cur:
        # Get all inventory_arch rows for this sale (activity_id)
        cur.execute("SELECT i.cnt, i.price FROM archive_sold a JOIN inventory_arch i on a.inventory_id = i.id WHERE a.activity_id = %s AND i.symbol = %s", (activity_id, symbol))
        rows = cur.fetchall()
        total_cost = 0
        total_qty = 0
        for cnt, purchase_price in rows:
            total_cost += float(cnt) * float(purchase_price)
            total_qty += int(cnt)
        if total_qty > 0:
            avg_purchase_price = total_cost / total_qty
            total_sale = float(sale_price) * total_qty
            profit = total_sale - total_cost
            return {
                'message': f"--- {symbol}: sold {total_qty} @ {sale_price:.4f}, total sale ${total_sale:.2f}, cost ${total_cost:.2f}, profit ${profit:.2f}",
                'short_message': f"Sold {total_qty} {symbol} @ ${sale_price:.2f}, profit ${profit:.2f}"
            }
        else:
            return {
                'message': f"Sale summary for {symbol}: No matching inventory found for sale.",
                'short_message': f"Sold {sale_count} {symbol} @ ${sale_price:.2f} (no inventory found)"
            }

@with_reconnect
def log_action(conn, action, price, count, order_id, i_date, symbol, price_requested):
    """
    This function calls the MySQL stored procedure log_action
    """
    
    with conn.cursor() as cur:
        # Call the stored procedure with updated parameters
        cur.callproc('log_action', (
            action,
            count,
            price,
            i_date,
            symbol,
            order_id if order_id is not None else 0,
            price_requested,
            0  # Output parameter for activity_id
        ))
        
        # Get the output parameter (activity_id)
        cur.execute("SELECT @_log_action_7")
        activity_id = cur.fetchone()
        
        conn.commit()
        
        # Return appropriate summary based on action
        if action == 'buy':
            return get_purchase_summary(conn, symbol, count, price, activity_id)
        elif action == 'sell' or action == 'sellavg':
            return get_sale_summary(conn, symbol, count, price, activity_id)
        else:
            return None
