# E*TRADE OAuth Workflow

This application now supports a two-step OAuth authentication process that saves request tokens to a file and allows you to complete authentication with a verification code passed as a command line argument.

## How it works

### Step 1: Get Authorization URL
Run the application without any arguments:

```bash
python app.py
```

This will:
1. Generate OAuth request tokens
2. Save the tokens to `oauth_tokens.json`
3. Display the authorization URL
4. Open the URL in your browser
5. Exit with instructions

Example output:
```
Request tokens saved to oauth_tokens.json
Authorization URL:
https://us.etrade.com/e/t/etws/authorize?key=YOUR_KEY&token=YOUR_TOKEN

Please visit the URL above, accept the agreement, and get the verification code.
Then run this app again with the verification code as a command line argument.
Example: python app.py YOUR_VERIFICATION_CODE
```

### Step 2: Complete Authentication
After visiting the URL and getting your verification code from E*TRADE:

```bash
python app.py YOUR_VERIFICATION_CODE
```

This will:
1. Load the saved request tokens from `oauth_tokens.json`
2. Use your verification code to complete OAuth authentication
3. Clean up the saved tokens file
4. Continue with normal application execution

## Files Created

- `oauth_tokens.json` - Temporarily stores request token and secret (automatically deleted after successful authentication)

## Error Handling

If authentication fails with an invalid verification code, you'll see an error message and need to restart the process from Step 1.

## Benefits

- **Separation of concerns**: Get the auth URL first, then authenticate separately
- **Scriptable**: Can be automated or integrated into other workflows
- **Secure**: Request tokens are only stored temporarily and cleaned up after use
- **User-friendly**: Clear instructions and error messages

## Example Workflow

```bash
# Step 1: Get authorization URL
$ python app.py
Request tokens saved to oauth_tokens.json
Authorization URL:
https://us.etrade.com/e/t/etws/authorize?key=...&token=...

# User visits URL, accepts agreement, gets code: ABC123

# Step 2: Complete authentication and run app
$ python app.py ABC123
Using verification code from command line: ABC123
OAuth session established successfully!
Cleaned up saved tokens file: oauth_tokens.json
# App continues normal execution...
```
