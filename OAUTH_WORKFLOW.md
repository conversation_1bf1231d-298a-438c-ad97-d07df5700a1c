# E*TRADE OAuth Workflow

This application supports multiple authentication modes with a comprehensive command line interface.

## Command Line Options

```bash
python app.py --help
```

Available options:
- `--mode {prod,sandbox,none}` - Override environment setting from config file
- `--oauth` - Use OAuth workflow (get URL or authenticate with code)
- `--code CODE` - OAuth verification code from E*TRADE
- `--test` - Test mode (don't delete OAuth token file after authentication)

## Authentication Modes

### Traditional Interactive Mode (Default)
Run without any arguments for the original interactive behavior:

```bash
python app.py
# or
python app.py --mode sandbox
```

This will prompt for verification code interactively and continue running.

### OAuth Workflow Mode

#### Step 1: Get Authorization URL
```bash
python app.py --oauth
# or specify environment
python app.py --mode sandbox --oauth
```

This will:
1. Generate OAuth request tokens
2. Save the tokens to `oauth_tokens.json`
3. Display the authorization URL
4. Open the URL in your browser
5. Exit with instructions

#### Step 2: Complete Authentication
After visiting the URL and getting your verification code from E*TRADE:

```bash
python app.py --oauth --code YOUR_VERIFICATION_CODE
# or specify environment
python app.py --mode sandbox --oauth --code YOUR_VERIFICATION_CODE
```

This will:
1. Load the saved request tokens from `oauth_tokens.json`
2. Use your verification code to complete OAuth authentication
3. Clean up the saved tokens file
4. Continue with normal application execution

## Environment Override

You can override the config file environment setting:

```bash
python app.py --mode prod          # Force production mode
python app.py --mode sandbox       # Force sandbox mode
python app.py --mode none          # Disable E*TRADE API
```

## Test Mode

Use `--test` flag to prevent deletion of the OAuth tokens file (useful for testing):

```bash
python app.py --oauth --code ABC123 --test
```

## Files Created

- `oauth_tokens.json` - Temporarily stores request token and secret (automatically deleted after successful authentication unless `--test` is used)

## Error Handling

If authentication fails with an invalid verification code, you'll see an error message and need to restart the process from Step 1.

## Benefits

- **Flexible**: Multiple authentication modes to suit different workflows
- **Scriptable**: Can be automated or integrated into other workflows
- **Environment control**: Override config settings via command line
- **Test-friendly**: Test mode preserves tokens for debugging
- **Secure**: Request tokens are only stored temporarily and cleaned up after use
- **User-friendly**: Clear instructions and comprehensive help

## Example Workflows

### Traditional Interactive Workflow
```bash
$ python app.py --mode sandbox
# Prompts for verification code interactively, continues running
```

### OAuth Workflow
```bash
# Step 1: Get authorization URL
$ python app.py --mode sandbox --oauth
Request tokens saved to oauth_tokens.json
Authorization URL: https://us.etrade.com/e/t/etws/authorize?key=...&token=...

# User visits URL, accepts agreement, gets code: ABC123

# Step 2: Complete authentication and run app
$ python app.py --mode sandbox --oauth --code ABC123
OAuth session established successfully!
Cleaned up saved tokens file: oauth_tokens.json
# App continues normal execution...
```

### Testing Workflow
```bash
# Test without cleaning up tokens
$ python app.py --mode sandbox --oauth --code ABC123 --test
Test mode: Would have cleaned up saved tokens file: oauth_tokens.json
# Tokens file remains for debugging
```
